/* Custom CSS for MTS Nurul Huda Dau */

/* Top Header Styles */
.top-header {
    background: linear-gradient(135deg, #1e5799 0%, #2989d8 50%, #207cca 51%, #7db9e8 100%);
    color: white;
    padding: 15px 0;
    font-size: 14px;
}

.school-logo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.school-name {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    letter-spacing: 1px;
}

.school-tagline {
    font-size: 12px;
    opacity: 0.9;
    font-style: italic;
}

.contact-info {
    font-size: 13px;
}

.contact-item {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.contact-item:hover {
    opacity: 0.8;
    cursor: pointer;
}

.contact-item i {
    font-size: 14px;
    opacity: 0.9;
}

/* Main Navigation Styles */
.main-navbar {
    background-color: #1e5799;
    padding: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-navbar .navbar-nav {
    justify-content: flex-start;
}

.main-navbar .nav-link {
    color: white !important;
    font-weight: 500;
    font-size: 14px;
    padding: 15px 20px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.main-navbar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
}

.main-navbar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Home icon special styling */
.main-navbar .nav-item:first-child .nav-link {
    padding: 15px 25px !important;
}

.main-navbar .nav-item:first-child .nav-link i {
    font-size: 16px;
}

/* Dropdown Styles */
.main-navbar .dropdown-menu {
    background-color: #2989d8;
    border: none;
    border-radius: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: 0;
}

.main-navbar .dropdown-item {
    color: white;
    padding: 10px 20px;
    font-size: 13px;
    transition: background-color 0.3s ease;
}

.main-navbar .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Dropdown arrow styling */
.main-navbar .dropdown-toggle::after {
    margin-left: 8px;
    vertical-align: middle;
}

/* Navbar toggler for mobile */
.navbar-toggler {
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 4px 8px;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.8%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Hero Section with Slider */
.hero-section {
    position: relative;
    min-height: 500px;
    overflow: hidden;
}

.hero-section .carousel {
    height: 500px;
}

.hero-section .carousel-item {
    height: 500px;
}

.hero-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
            rgba(74, 85, 104, 0.6) 0%,
            rgba(45, 55, 72, 0.7) 50%,
            rgba(30, 87, 153, 0.8) 100%);
    display: flex;
    align-items: center;
    padding: 60px 0;
}

.hero-content {
    color: white;
    z-index: 2;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.95;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Carousel Indicators */
.hero-section .carousel-indicators {
    bottom: 80px;
    z-index: 3;
}

.hero-section .carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.hero-section .carousel-indicators button.active {
    background-color: #ffc107;
    border-color: #ffc107;
}

/* Carousel Controls */
.hero-section .carousel-control-prev,
.hero-section .carousel-control-next {
    width: 5%;
    z-index: 3;
}

.hero-section .carousel-control-prev-icon,
.hero-section .carousel-control-next-icon {
    width: 30px;
    height: 30px;
    background-size: 100%;
}

/* Quote Section - Absolute Position at Bottom */
.quote-section {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(90deg, #1e5799 0%, #2989d8 100%);
    padding: 15px 0;
    z-index: 4;
}

.quote-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    flex-wrap: wrap;
}

.quote-btn {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
    font-weight: bold;
    padding: 8px 20px;
    border-radius: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    text-decoration: none;
    font-size: 0.9rem;
}

.quote-btn:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: #000;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.quote-text {
    color: white;
    font-size: 0.9rem;
    line-height: 1.4;
    opacity: 0.95;
    flex: 1;
    min-width: 300px;
}

.quote-text strong {
    color: #ffc107;
    font-weight: bold;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Main content */
.main-content {
    min-height: 500px;
    padding: 40px 0;
    background-color: #f8f9fa;
}

/* Articles Section */
.articles-section {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 3px solid #1e5799;
}

.article-item {
    margin-bottom: 25px;
    padding-bottom: 25px;
    border-bottom: 1px solid #e9ecef;
}

.article-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.article-image img {
    border-radius: 6px;
    width: 100%;
    height: 180px;
    object-fit: cover;
}

.article-content {
    padding-left: 20px;
}

.article-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 12px;
    line-height: 1.4;
}

.article-excerpt {
    color: #6c757d;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    font-size: 0.85rem;
    color: #6c757d;
}

.article-meta span {
    display: flex;
    align-items: center;
}

.article-meta .btn {
    margin-left: auto;
    padding: 4px 8px;
}

/* Principal Welcome Section */
.principal-welcome {
    position: sticky;
    top: 20px;
}

.principal-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    text-align: center;
}

.principal-image {
    margin-bottom: 20px;
}

.principal-image img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e9ecef;
}

.principal-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
    letter-spacing: 0.5px;
}

.principal-title {
    color: #6c757d;
    font-style: italic;
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.principal-message {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 20px;
    text-align: justify;
}

.principal-card .btn {
    background-color: transparent;
    border: 1px solid #6c757d;
    color: #6c757d;
    font-size: 0.85rem;
    padding: 8px 20px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.principal-card .btn:hover {
    background-color: #1e5799;
    border-color: #1e5799;
    color: white;
}

/* Newsletter Section */
.newsletter-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.newsletter-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 3px solid #1e5799;
    position: relative;
}

.newsletter-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50%;
    height: 3px;
    background-color: #ffc107;
}

.newsletter-form .input-group {
    border: 2px solid #e9ecef;
    border-radius: 5px;
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    padding: 12px 15px;
    font-size: 0.95rem;
    background-color: #f8f9fa;
}

.newsletter-form .form-control:focus {
    box-shadow: none;
    background-color: white;
}

.newsletter-form .btn {
    background-color: #1e5799;
    border-color: #1e5799;
    padding: 12px 20px;
    border: none;
}

.newsletter-form .btn:hover {
    background-color: #2989d8;
    border-color: #2989d8;
}

/* Advertisement Section */
.ads-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin-bottom: 20px;
}

.ads-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 3px solid #1e5799;
    position: relative;
}

.ads-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50%;
    height: 3px;
    background-color: #ffc107;
}

.ads-section .carousel {
    border-radius: 6px;
    overflow: hidden;
}

.ads-image img {
    width: 100%;
    height: 207px;
    object-fit: cover;
    border-radius: 6px;
}

.ads-section .carousel-control-prev,
.ads-section .carousel-control-next {
    width: 15%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0;
}

.ads-section .carousel-control-prev-icon,
.ads-section .carousel-control-next-icon {
    width: 20px;
    height: 20px;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .top-header {
        text-align: center;
    }

    .contact-info {
        justify-content: center !important;
        margin-top: 15px;
        flex-wrap: wrap;
    }

    .contact-item {
        margin: 5px 10px;
    }

    .school-info {
        justify-content: center;
    }

    .main-navbar .nav-link {
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .main-navbar .nav-link:last-child {
        border-bottom: none;
    }

    /* Articles and Principal Section - Tablet */
    .articles-section {
        padding: 20px;
        margin-bottom: 20px;
    }

    .article-content {
        padding-left: 15px;
    }

    .principal-welcome {
        position: static;
        margin-top: 20px;
    }

    .principal-card {
        padding: 20px;
    }

    /* Newsletter and Ads Section - Tablet */
    .newsletter-section,
    .ads-section {
        padding: 20px;
        margin-top: 15px;
        margin-bottom: 15px;
    }

    .newsletter-title,
    .ads-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 767.98px) {
    .school-name {
        font-size: 16px;
    }

    .school-tagline {
        font-size: 11px;
    }

    .contact-info {
        font-size: 12px;
    }

    .contact-item {
        margin: 3px 8px;
    }

    .main-navbar .nav-link {
        font-size: 13px;
        padding: 12px 15px !important;
    }

    /* Hero Section Mobile */
    .hero-section {
        min-height: 400px;
    }

    .hero-section .carousel {
        height: 400px;
    }

    .hero-section .carousel-item {
        height: 400px;
    }

    .hero-overlay {
        padding: 40px 0;
    }

    .hero-title {
        font-size: 1.8rem;
        margin-bottom: 15px;
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .hero-section .carousel-indicators {
        bottom: 60px;
    }

    .quote-section {
        padding: 12px 0;
    }

    .quote-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .quote-btn {
        padding: 6px 15px;
        font-size: 0.8rem;
        align-self: flex-start;
    }

    .quote-text {
        font-size: 0.8rem;
        min-width: auto;
        width: 100%;
    }

    /* Articles and Principal Section - Mobile */
    .articles-section {
        padding: 15px;
        margin-bottom: 15px;
    }

    .section-title {
        font-size: 1.3rem;
        margin-bottom: 20px;
    }

    .article-item {
        margin-bottom: 20px;
        padding-bottom: 20px;
    }

    .article-content {
        padding-left: 0;
        margin-top: 15px;
    }

    .article-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .article-excerpt {
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .article-meta {
        gap: 10px;
        font-size: 0.8rem;
    }

    .article-meta .btn {
        margin-left: 0;
        margin-top: 5px;
    }

    .principal-card {
        padding: 15px;
    }

    .principal-image img {
        width: 120px;
        height: 120px;
    }

    .principal-name {
        font-size: 1.1rem;
    }

    .principal-message {
        font-size: 0.85rem;
    }

    /* Newsletter and Ads Section - Mobile */
    .newsletter-section,
    .ads-section {
        padding: 15px;
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .newsletter-title,
    .ads-title {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }

    .newsletter-form .form-control {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .newsletter-form .btn {
        padding: 10px 15px;
    }

    .ads-image img {
        height: 150px;
    }

    /* Footer Section - Mobile */
    .footer-section {
        padding: 30px 0 0 0;
        margin-top: 30px;
    }

    .footer-column {
        margin-bottom: 25px;
    }

    .footer-title {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }

    .footer-description {
        font-size: 0.85rem;
        margin-bottom: 15px;
    }

    .footer-content .contact-item {
        margin-bottom: 12px;
        gap: 10px;
    }

    .contact-details strong {
        font-size: 0.85rem;
    }

    .contact-details span {
        font-size: 0.8rem;
    }

    .tags-container {
        gap: 8px;
    }

    .tag-item {
        padding: 6px 12px;
        font-size: 0.75rem;
    }

    .social-links {
        gap: 10px;
    }

    .social-link {
        font-size: 0.85rem;
        padding: 6px 0;
    }

    .footer-bottom {
        padding: 15px 0;
        text-align: center;
    }

    .powered-by {
        text-align: center;
        margin-top: 10px;
    }

    .copyright p,
    .powered-by p {
        font-size: 0.75rem;
    }
}

/* Footer Section */
.footer-section {
    background: linear-gradient(135deg, #1e5799 0%, #2989d8 100%);
    color: white;
    padding: 40px 0 0 0;
    margin-top: 40px;
}

.footer-column {
    margin-bottom: 30px;
}

.footer-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
}

.footer-description {
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 20px;
    opacity: 0.9;
}

/* Contact Items */
.footer-content .contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    gap: 12px;
}

.footer-content .contact-item i {
    font-size: 1.1rem;
    margin-top: 2px;
    opacity: 0.8;
    width: 20px;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.contact-details strong {
    font-size: 0.9rem;
    font-weight: bold;
}

.contact-details span {
    font-size: 0.85rem;
    opacity: 0.9;
}

/* Tags Section */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.tag-item {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tag-item:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

/* Social Links */
.social-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 8px 0;
}

.social-link:hover {
    color: #ffc107;
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* Footer Bottom */
.footer-bottom {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 20px 0;
    margin-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.8;
    line-height: 1.4;
}

.powered-by {
    text-align: right;
}

.powered-by p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.8;
}
}

/* Additional hover effects */
.main-navbar .nav-item:hover .nav-link {
    transform: translateY(-1px);
}

/* Smooth transitions */
* {
    transition: all 0.3s ease;
}

/* Custom scrollbar for better aesthetics */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #1e5799;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2989d8;
}